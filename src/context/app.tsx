import React, { createContext, useContext, useState } from "react";

interface AppContextType {
  sideFold: boolean;
  setSideFold: React.Dispatch<React.SetStateAction<boolean>>;
}

const AppContext = createContext<AppContextType>({
  sideFold: true,
  setSideFold: () => { },
});

const AppContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [sideFold, setSideFold] = useState(true)

  return (
    <AppContext.Provider value={{ sideFold, setSideFold }}>
      {children}
    </AppContext.Provider>
  )
}


export { AppContext, AppContextProvider }
