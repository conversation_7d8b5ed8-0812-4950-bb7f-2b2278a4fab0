// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion } from "framer-motion";
import { FastForward, Play } from "lucide-react";
import { useCallback, useRef, useState } from "react";
import Header from './header'
import StopCircle from '@/assets/stop-circle.svg'

import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { fastForwardReplay } from "~/core/api";
import { useReplayMetadata } from "~/core/api/hooks";
import type { Option, Resource } from "~/core/messages";
import { useReplay } from "~/core/replay";
import { sendMessage, useMessageIds, useStore } from "~/core/store";
import { env } from "~/env";
import { cn } from "~/lib/utils";

import { ConversationStarter } from "./conversation-starter";
import { InputBox } from "./input-box";
import { MessageListView } from "./message-list-view";
import { Welcome } from "./welcome";
import ChatInputArea from "./chat-input-area";
import Image from "next/image";

export function MessagesBlock({ className }: { className?: string }) {
  const messageIds = useMessageIds();
  const messageCount = messageIds.length;
  const responding = useStore((state) => state.responding);
  const { isReplay } = useReplay();
  const { title: replayTitle, hasError: replayHasError } = useReplayMetadata();
  const [replayStarted, setReplayStarted] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [feedback, setFeedback] = useState<{ option: Option } | null>(null);
  const isMobile = false
  const embedSource = true
  const aiBtmGradientFrom = '#d4ddfa' // AI底部背景渐变色起始
  const aiBtmBgGradientTo = '#fafafd' // AI底部背景渐变色结束

  const handleSend = useCallback(
    async (
      message: string,
      options?: {
        interruptFeedback?: string;
        resources?: Array<Resource>;
      },
    ) => {
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      try {
        await sendMessage(
          message,
          {
            interruptFeedback:
              options?.interruptFeedback ?? feedback?.option.value,
            resources: options?.resources,
          },
          {
            abortSignal: abortController.signal,
          },
        );
      } catch { }
    },
    [feedback],
  );
  const handleCancel = useCallback(() => {
    abortControllerRef.current?.abort();
    abortControllerRef.current = null;
  }, []);
  const handleFeedback = useCallback(
    (feedback: { option: Option }) => {
      setFeedback(feedback);
    },
    [setFeedback],
  );
  const handleRemoveFeedback = useCallback(() => {
    setFeedback(null);
  }, [setFeedback]);
  const handleStartReplay = useCallback(() => {
    setReplayStarted(true);
    void sendMessage();
  }, [setReplayStarted]);
  const [fastForwarding, setFastForwarding] = useState(false);
  const handleFastForwardReplay = useCallback(() => {
    setFastForwarding(!fastForwarding);
    fastForwardReplay(!fastForwarding);
  }, [fastForwarding]);

  return (
    <div className={cn("flex h-full flex-col bg-[#fafafd]", className)}>
      <div className="flex-1 flex flex-col h-full">
        <Header />
        <MessageListView
          className="flex flex-grow px-[15.8vw] pt-6 pb-[200px]"
          onFeedback={handleFeedback}
          onSendMessage={handleSend}
        />
      </div>

      <div className="h-42 shrink-0 pb-12 absolute bottom-0 justify-center bg-chat-input-mask px-[15.8vw] w-full" style={{ background: 'linear-gradient(0deg, rgb(212, 221, 250), rgb(250, 250, 253) 60%)' }}>
        {responding && (
          <div className='mb-2 flex justify-center'>
            <Button className="bg-white border border-solid border-[#10182824] px-[14px] gap-0" onClick={handleCancel}>
              <Image src={StopCircle} width='18' height='18' alt="" />
              <span className='text-xs font-normal text-gray-500 ml-[5px]'>{'停止响应'}</span>
            </Button>
          </div>
        )}
        <div className="relative z-10 rounded-xl border-components-chat-input-border bg-components-panel-bg-blur pb-[9px] shadow-md border-[2px] !border-[#1E86FF] w-full border-solid">
          <InputBox
            className="h-full w-full"
            responding={responding}
            feedback={feedback}
            onSend={handleSend}
            onCancel={handleCancel}
            onRemoveFeedback={handleRemoveFeedback}
          />
        </div>

      </div>
    </div>
  );
}
