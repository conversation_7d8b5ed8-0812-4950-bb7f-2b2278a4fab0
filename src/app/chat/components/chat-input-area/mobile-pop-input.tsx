import cn from 'classnames'
import IconMinify from '@/assets/minify.svg'
import Image from 'next/image'
import { type ReactElement, useEffect, useState } from 'react'

type Props = {
    visible: boolean
    children: ReactElement
    onClose: () => void
}

const MobilePopInput = ({ visible, children, onClose }: Props) => {
    const [isPop, setIsPop] = useState(false)

    useEffect(() => {
        setTimeout(() => {
            setIsPop(visible)
        }, 100)
    }, [visible])

    if (!visible && !isPop)
        return null

    return (
        <div className={cn('fixed left-[0px] top-[0px] z-[999] flex h-full w-full items-end bg-[#0009] transition-opacity duration-100 ease-linear')}>
            <div className={cn('relative flex h-[50%] w-full flex-col rounded-t-[8px] bg-white transition-all duration-100 ease-linear', (!isPop || (isPop && !visible)) ? 'translate-y-[300px] opacity-0' : 'opacity-1')}>
                <div className='flex justify-end p-[10px]'>
                    <Image className='right-[10px] top-[10px] cursor-pointer' src={IconMinify} width='20' height="20" alt="" onClick={onClose}/>
                </div>
               <div className='flex-1 overflow-auto'>
                    {children}
               </div>
            </div>
        </div>
    )
}

export default MobilePopInput
