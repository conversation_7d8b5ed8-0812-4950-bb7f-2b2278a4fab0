import { memo } from 'react'
import {
  RiMicLine,
  RiSendPlane2Fill,
} from '@remixicon/react'
import type {
  EnableType,
} from '../../types'
import type { Theme } from '../../embedded-chatbot/theme/theme-context'
import { useChatWithHistoryContext } from '../../chat-with-history/context'
import { INTENT_DOCUMENT_ANALYSIS, INTENT_FREE_TALK } from '@/app/components/base/chat/chat/u-const'
import Button from '@/app/components/base/button'
import ActionButton from '@/app/components/base/action-button'
import FileUploaderInChatInput from '@/app/components/base/file-uploader/file-uploader-in-chat-input'
import type { FileUpload } from '@/app/components/base/features/types'
import cn from '@/utils/classnames'
import type { EmbedSource } from '@/models/share'
import { EMBED_SOURCE_TYPE } from '@/config'

type OperationProps = {
  fileConfig?: FileUpload
  speechToTextConfig?: EnableType
  onShowVoiceInput?: () => void
  onSend: () => void
  theme?: Theme | null
  intent?: string
  embedSource?: EmbedSource
  isMobile?: boolean
}
const Operation = (
  {
    ref,
    fileConfig,
    speechToTextConfig,
    onShowVoiceInput,
    onSend,
    theme,
    intent,
    embedSource,
    isMobile,
  }: OperationProps & {
    ref: React.RefObject<HTMLDivElement>;
  },
) => {
  const { appData } = useChatWithHistoryContext()
  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  const btnBgGradientFrom = chatPageConfigData?.btnBgGradientFrom || '#5099FF'
  const btnBgGradientTo = chatPageConfigData?.btnBgGradientTo || '#7D67FF'

  /**
   * intent为INTENT_DOCUMENT_ANALYSIS，embedSource为lark(中海)，则显示文件上传按钮；
   * intent为INTENT_FREE_TALK，embedSource为wework(中建)，则显示文件上传按钮；
   */
  function shouldShowUpload(embedSource: string | undefined, intent: string | undefined): boolean {
    if (!embedSource || !intent)
      return false // 如果 embedSource 或 intent 为空，直接返回 false

    const validCombinations: { [key: string]: string[] } = {
      [EMBED_SOURCE_TYPE.coli688]: [INTENT_DOCUMENT_ANALYSIS],
      [EMBED_SOURCE_TYPE.FS]: [INTENT_DOCUMENT_ANALYSIS],
      [EMBED_SOURCE_TYPE.ZJT]: [INTENT_FREE_TALK],
      [EMBED_SOURCE_TYPE.ZJ4A]: [INTENT_FREE_TALK],
    }

    // 检查是否存在有效的组合
    return validCombinations[embedSource]?.includes(intent) ?? false
  }
  const isShowUpload = shouldShowUpload(embedSource, intent)

  return (
    <div
      className={cn(
        'flex shrink-0 items-center justify-end',
      )}
    >
      <div
        className='flex items-center pl-1'
        ref={ref}
      >
        <div className='flex items-center space-x-1'>
          {
            embedSource
              ? (isShowUpload && fileConfig?.enabled && <FileUploaderInChatInput fileConfig={fileConfig}/>)
              : (fileConfig?.enabled && <FileUploaderInChatInput fileConfig={fileConfig}/>)
          }
          {
            speechToTextConfig?.enabled && (
              <ActionButton
                size='l'
                onClick={onShowVoiceInput}
              >
                <RiMicLine className='h-5 w-5' />
              </ActionButton>
            )
          }
        </div>
        <Button
          className={`ml-3 w-8 px-0 ${embedSource && isMobile && 'rounded-full'}`}
          variant='primary'
          onClick={onSend}
          style={
            theme
              ? {
                backgroundColor: theme.primaryColor,
                background: `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})`,
              }
              : {}
          }
        >
          <RiSendPlane2Fill className='h-4 w-4' />
        </Button>
      </div>
    </div>
  )
}
Operation.displayName = 'Operation'

export default memo(Operation)
