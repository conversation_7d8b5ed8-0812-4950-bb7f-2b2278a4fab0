import {
  RiEditBoxLine,
  RiExpandRightLine,
  RiLayoutLeft2Line,
} from '@remixicon/react'
import Input from '@/app/chat/components/input'
// import BtnFold from '../btn-fold'
// import ActionButton from '@/app/components/base/action-button'
// import Button from '@/app/components/base/button'
import List from './list'
import cn from 'classnames'
// import styles from '../index.module.css'
import Image from 'next/image'
import { useContext } from 'react'
import { AppContext } from '~/context/app'

const LarkDeleteIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" className={className}>
    <g stroke="#A3AFBB" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd">
      <path d="M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667" transform="translate(2.3333 1.4583)" />
      <path d="M0 2.04166667L9.33333333 2.04166667" transform="translate(2.3333 1.4583)" />
      <path d="M3.5 0.29166667L5.83333333 0.29166667" transform="translate(2.3333 1.4583)" />
      <path d="M3.5 7.875L3.5 4.95833333" transform="translate(2.3333 1.4583)" />
      <path d="M5.83333333 7.875L5.83333333 4.95833333" transform="translate(2.3333 1.4583)" />
    </g>
  </svg>
)

const IconFold = ({ className }: { className: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
      <g stroke="#1E6AFF" stroke-width="1.4" fill="none" fill-rule="evenodd">
        <rect x=".7" y=".7" width="18.6" height="18.6" rx="4" />
        <path d="M13.5 0L13.5 20" />
        <path d="M6,7 L9,10 L6,13" transform="matrix(-1 0 0 1 15 0)" />
      </g>
    </svg>
  )

type Props = {
  isPanel?: boolean
}

const Sidebar = ({ isPanel }: Props) => {
    const isEmbedMobile = false
    const embedSource = true
    const isMobile = false
    const {sideFold,setSideFold}  = useContext(AppContext)
    

   if(sideFold) return null


  return (
    <div className={cn(
      'flex w-[236px] flex-col shrink-0',
      isPanel && 'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg',
    //   isEmbedMobile && styles.bg,
      isEmbedMobile && 'bg-[#f5f6f8] w-[74vw] !border-none',
      'bg-[#fff]'
    )}>
      <div className={cn(
        'flex shrink-0 items-center gap-3 p-3 pr-2',
      )}>
        <div className='w-full shrink-0'>
          {
            embedSource ? (
              !isMobile && <div className="flex items-center">
                <Input
                  className='h-[40px] rounded-[8px] border-[1px] !border-solid !border-[#DFE4E8] flex-1'
                  value={''}
                  placeholder="搜索对话..."
                  showLeftIcon
                  showClearIcon
                  onChange={() => {}}
                  onClear={() => {}}
                />
                <div onClick={() => setSideFold(!sideFold)}>
                  <IconFold className="w-[18px] ml-[18px] shrink-0 cursor-pointer" />
                </div>
              </div>
            ) : (
              <>
                {/* <AppIcon
                  size='large'
                  iconType={appData?.site.icon_type}
                  icon={appData?.site.icon}
                  background={appData?.site.icon_background}
                  imageUrl={appData?.site.icon_url}
                /> */}
                <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{'title'}</div>
                {/* {!isMobile && (
                  <ActionButton size='l' onClick={() => {}}>
                    <RiExpandRightLine className='h-[18px] w-[18px]' />
                  </ActionButton>
                )}
                {!isMobile  && (
                  <ActionButton size='l' onClick={() => {}}>
                    <RiLayoutLeft2Line className='h-[18px] w-[18px]' />
                  </ActionButton>
                )} */}
              </>
            )
          }
        </div>
      </div>
      {
        !embedSource && (
          <div className='shrink-0 px-3 py-4'>
            {/* <Button variant='secondary-accent' className='w-full justify-center' onClick={() => {}}>
              <RiEditBoxLine className='mr-1 h-4 w-4' />
             
            </Button> */}
          </div>
        )
      }
      {embedSource && isMobile && <div className="mb-[30px]">
        <Image src={''} width={100} height={100} className="w-[100px] height-[100px] mt-[64px] mx-auto mb-[6px]" alt="" />
        <p className="text-center text-[18px] text-[#242933] mb-[20px] font-semibold">Hi～我是</p>
        <div className="px-[12px]">
          <Input
            className='h-[40px] rounded-[8px] border-[1px] border-solid bg-[#fff] hover:bg-[#fff]'
            value={''}
            placeholder="搜索对话..."
            showLeftIcon
            showClearIcon
            onChange={() => {}}
            onClear={() => {}}
          />
        </div>
      </div>}
      <div className="flex items-center justify-between px-[20px] mb-[10px]">
        <p className="text-[16px] text-[#242933] font-medium">对话记录</p>
        <button className='bg-transparent border-0 cursor-pointer' onClick={() => {}}><LarkDeleteIcon className="w-[20px] h-[20px]" /></button>
      </div>
      <div className='h-0 grow space-y-2 overflow-y-auto px-3 pt-4'>
        {/* pinned list */}
        {/* {!!pinnedConversationList.length && (
          <div className='mb-4'>
            <List
              embedSource={embedSource}
              isMobile={isMobile}
              isPin
              title={t('share.chat.pinnedTitle') || ''}
              list={pinnedConversationList.filter(item => item.name.includes(keyword))}
              onChangeConversation={handleChangeConversation}
              onOperate={handleOperate}
              currentConversationId={currentConversationId}
            />
          </div>
        )} */}
          <List
            embedSource={embedSource}
            isMobile={isMobile}
            title={''}
            list={[{name: 'test'},{name: 'test2'}]}
            onChangeConversation={() => {}}
            onOperate={() => {}}
            currentConversationId={''}
          />
      </div>
      <div className='flex shrink-0 items-center justify-between p-3'>
      </div>
    </div>
  )
}

export default Sidebar
