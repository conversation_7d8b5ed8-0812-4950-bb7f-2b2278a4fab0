import type { FC } from 'react'
import {
  memo,
  useRef,
} from 'react'
import { useHover } from 'ahooks'
import cn from 'classnames'


type ItemProps = {
  isPin?: boolean
  item: any
  embedSource?: string
  isMobile?: boolean
  onOperate: (type: string, item: any) => void
  onChangeConversation: (conversationId: string) => void
  currentConversationId: string
}

const LarkChatIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" className={className}>
    <path d="M4.58035669,13.2913263 L5.50680349,12.4266908 L5.51361828,12.4205913 C5.70676076,12.2403023 5.8042226,12.1493067 5.91296138,12.084553 C6.01052049,12.0263953 6.11454787,11.9840536 6.22185803,11.9583082 C6.34281601,11.9293711 6.46881365,11.9293711 6.72173297,11.9293711 L11.9991107,11.9293711 C12.6787052,11.9293711 13.0188976,11.9293711 13.2787229,11.7748985 C13.5074835,11.638937 13.6936288,11.4216966 13.8101672,11.1548093 C13.9425723,10.8516798 13.9425723,10.4552142 13.9425723,9.66235387 L13.9425723,4.26737899 C13.9425723,3.47450452 13.9425723,3.07747862 13.8101672,2.7743491 C13.6936288,2.50744051 13.5071187,2.29059723 13.2783581,2.1546074 C13.0182897,2 12.6782796,2 11.9973477,2 L4.94546777,2 C4.26452973,2 3.92380843,2 3.6637278,2.1546074 C3.43494901,2.29059723 3.24908334,2.50744051 3.13252063,2.7743491 C3,3.0777765 3,3.47528469 3,4.2697124 L3,12.4054136 C3,13.1612515 3,13.5390641 3.13280635,13.7331833 C3.2483052,13.9019826 3.42338028,14.0002124 3.60851644,14 C3.82139203,13.999716 4.07446333,13.7635388 4.58035669,13.2913263 Z" stroke="#B9C2CB" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1" fill="none" fill-rule="evenodd" />
  </svg>

)

const Item: FC<ItemProps> = ({
  isPin,
  item,
  embedSource,
  isMobile,
  onOperate,
  onChangeConversation,
  currentConversationId,
}) => {
  const ref = useRef(null)
  const isHovering = useHover(ref)
  const isSelected = currentConversationId === item.id

  return (
    <div
      ref={ref}
      key={item.id}
      className={cn(
        'system-sm-medium group flex cursor-pointer rounded-lg p-1 pl-3 text-components-menu-item-text hover:bg-state-base-hover',
        isSelected && 'bg-state-accent-active text-text-accent hover:bg-state-accent-active',
        (currentConversationId === item.id && !embedSource) && 'text-primary-600 bg-primary-50',
        (currentConversationId === item.id && embedSource) && 'bg-[#ECECEC]'
      )}
      onClick={() => onChangeConversation(item.id)}
    >
      {embedSource && !isMobile && <LarkChatIcon className="w-[16px] mr-[6px] shrink-0" />}
      <div className={`grow truncate p-1 pl-0 ${embedSource && 'overflow-hidden text-ellipsis text-nowrap'}`} title={item.name}>{item.name}</div>
    </div>
  )
}

export default memo(Item)
