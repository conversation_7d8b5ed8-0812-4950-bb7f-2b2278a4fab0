import type { FC } from 'react'
import Item from './item'

type ListProps = {
  isPin?: boolean
  title?: string
  list:[]
  embedSource?: string
  isMobile?: boolean
  onOperate: (type: string, item: any) => void
  onChangeConversation: (conversationId: string) => void
  currentConversationId: string
}
const List: FC<ListProps> = ({
  isPin,
  title,
  list,
  embedSource,
  isMobile,
  onOperate,
  onChangeConversation,
  currentConversationId,
}) => {
  return (
    <div className='space-y-0.5'>
      {list.map(item => (
        <Item
          isMobile={isMobile}
          embedSource={embedSource}
          key={item.id}
          isPin={isPin}
          item={item}
          onOperate={onOperate}
          onChangeConversation={onChangeConversation}
          currentConversationId={currentConversationId}
        />
      ))}
    </div>
  )
}

export default List
