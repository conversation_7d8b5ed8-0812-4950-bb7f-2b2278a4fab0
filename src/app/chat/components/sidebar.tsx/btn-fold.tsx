import cls from 'classnames'
import Button from '../button'
const IconFold = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <g stroke="#1E6AFF" stroke-width="1.4" fill="none" fill-rule="evenodd">
      <rect x=".7" y=".7" width="18.6" height="18.6" rx="4" />
      <path d="M13.5 0L13.5 20" />
      <path d="M6,7 L9,10 L6,13" transform="matrix(-1 0 0 1 15 0)" />
    </g>
  </svg>
)
const IconUnfold = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <g stroke="#1E6AFF" stroke-width="1.4" fill="none" fill-rule="evenodd">
      <rect x=".7" y=".7" width="18.6" height="18.6" rx="4" />
      <path d="M13.5 0L13.5 20" />
      <path d="M6,7 L9,10 L6,13" />
    </g>
  </svg>
)

const SvgClass = 'w-[20px] h-[20px]'

type Props = {
  className?: string
  isFold?: boolean
  onClick?: () => void
}

const BtnFold = ({ className, isFold, onClick }: Props) => {
  return (
    <div className={cls(className, 'cursor-pointer')} onClick={onClick}>
      {isFold
        ? <Button
          variant='secondary-accent'
          className={'h-[40px] rounded-[20px] border-[#356CFF] text-[#434B5B]'}
        >
          <IconUnfold className="w-[18px] mr-[8px]" />
        历史对话
        </Button>
        : <IconFold className={SvgClass} />}
    </div>
  )
}

export default BtnFold
