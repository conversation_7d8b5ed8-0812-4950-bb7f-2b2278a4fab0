import Image from "next/image"
import Button from "../button"
import BtnFold from "../sidebar.tsx/btn-fold"
import LarkAppLogo from '@/assets/lark-app-logo.svg'
import { useContext } from "react"
import { AppContext } from "~/context/app"
import { resetConversation } from "~/core/store"

const NewChatIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <defs>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="ca3toeybn__hi9n9hsm3a">
        <stop stop-color="#40A4FF" offset="0%" />
        <stop stop-color="#1E6AFF" offset="100%" />
      </linearGradient>
    </defs>
    <path d="M9,12 L9,9 M9,9 L9,6 M9,9 L6,9 M9,9 L12,9 M9.0001,18 C7.365,18 5.83174,17.5639 4.51025,16.8018 C4.3797,16.7265 4.31434,16.6888 4.25293,16.6719 C4.19578,16.6561 4.14475,16.6507 4.08559,16.6548 C4.02253,16.6591 3.9573,16.6808 3.82759,16.7241 L1.51807,17.4939 L1.51625,17.4947 C1.02892,17.6572 0.7848,17.7386 0.62256,17.6807 C0.4812,17.6303 0.36979,17.5187 0.31938,17.3774 C0.26157,17.2152 0.34268,16.9719 0.50489,16.4853 L0.50586,16.4823 L1.27468,14.1758 L1.27651,14.171 C1.31936,14.0424 1.34106,13.9773 1.34535,13.9146 C1.3494,13.8554 1.34401,13.804 1.32821,13.7469 C1.31146,13.6863 1.27448,13.6221 1.20114,13.495 L1.19819,13.4899 C0.43604,12.1684 0,10.6351 0,9 C0,4.02944 4.02944,0 9,0 C13.9706,0 18,4.02944 18,9 C18,13.9706 13.9707,18 9.0001,18 Z" transform="translate(1 1)" stroke="url(#ca3toeybn__hi9n9hsm3a)" stroke-width="1.6" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
)

const Header = () => {
  const { sideFold, setSideFold } = useContext(AppContext)
  const handleNewConversation = () => {
    resetConversation();
  }
  const pageBgColorFrom = 'rgb(215, 218, 252)'
  const pageBgColorTo = 'rgb(250, 250, 253)'
  const styles = {
    navBarBg: 'bg-gradient-to-b from-[var(--start-color)] to-[var(--end-color)]',

  }

  return (
    <div className={`relative sticky top-0 z-10 flex shrink-0 overflow-hidden px-[15.8vw] py-[16px] ${styles.navBarBg}`}
      style={{
        // 注入 CSS 变量
        '--start-color': pageBgColorFrom,
        '--end-color': pageBgColorTo,
      } as React.CSSProperties}>
      {/* {sideFold && <BtnFold className="absolute left-[10px] top-1/2 mr-[10px] flex -translate-y-1/2 items-center" isFold={sideFold} onClick={() => setSideFold(false)} />} */}

      <Button
        variant='secondary-accent'
        className={`absolute ${sideFold ? 'left-[10px]' : 'left-[12px]'}  z-11 top-1/2 h-[40px] w-[123px] -translate-y-1/2 justify-start rounded-[20px] border-[#356CFF] text-[#434B5B]`}
        onClick={handleNewConversation}>
        <NewChatIcon className="mr-[8px] w-[18px]" />
        开始新对话
      </Button>
      <div className='flex flex-1 items-center justify-center'>
        <div className="flex items-center">
          <Image src={LarkAppLogo} className='h-[34px]' alt='logo' />
        </div>
      </div>
    </div>
  )
}

export default Header