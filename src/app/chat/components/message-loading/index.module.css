.lark-loader {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6B4EFF;
    position: relative;
}

.lark-loader:before,
.lark-loader:after {
    content: "";
    position: absolute;
    border-radius: 50%;
    inset: 0;
    background: #AABDFF;
    transform: rotate(0deg) translate(10px);
    animation: rotate 1s ease infinite;
}

.lark-loader:after {
    animation-delay: 0.5s;
}

@keyframes rotate {
    from {}

    to {
        transform: rotate(360deg) translate(10px)
    }
}