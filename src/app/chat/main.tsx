// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useMemo } from "react";

import { useStore } from "~/core/store";
import { cn } from "~/lib/utils";

import { MessagesBlock } from "./components/messages-block";
import { ResearchBlock } from "./components/research-block";
import { ToastProvider } from "~/components/toast";

export default function Main() {
  const openResearchId = useStore((state) => state.openResearchId);
  const doubleColumnMode = useMemo(
    () => openResearchId !== null,
    [openResearchId],
  );
  return (
    <div
      className={cn(
        "flex h-full justify-center-safe flex-1",
        doubleColumnMode && "gap-8",
      )}
    >
      <ToastProvider>
        <MessagesBlock
          className={cn(
            "relative shrink-1 transition-all duration-300 ease-out w-full",
          )}
        />
        <ResearchBlock
          className={cn(
            "w-[min(max(calc((100vw-538px)*0.75),575px),960px)] pb-4 transition-all duration-300 ease-out",
            !doubleColumnMode && "scale-0 w-0",
            doubleColumnMode && "",
          )}
          researchId={openResearchId}
        />
      </ToastProvider>
    </div>
  );
}
